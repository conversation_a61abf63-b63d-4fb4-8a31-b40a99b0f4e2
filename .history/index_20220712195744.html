﻿<!DOCTYPE html>
<html lang="en" class="no-js">

<head>
	<meta charset="UTF-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>M.F.M Fazrin's Profile & Portfolio</title>
	<meta name="description" content="Portfolio of Senior Fullstack Engineer - M.F.M Fazrin">
	<meta name="keywords" content="Portfolio, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Senior, fullstack, Engineer, javascript, css, html">
	<meta name="author" content="Farook Fazrin">
	<link rel="stylesheet" type="text/css" href="css/normalize.css">
	<link rel="stylesheet" type="text/css" href="css/jquery-ui.css">
	<link rel="stylesheet" type="text/css" href="css/bootstrap.css">
	<link rel="stylesheet" type="text/css" href="css/interactiveHeader.css">
	<link rel="stylesheet" type="text/css" href="css/ionicons.css">
	<link rel="stylesheet" type="text/css" href="css/main.css">
	<link href="https://fonts.googleapis.com/css?family=Raleway:200,300,400,700|Roboto:300,400,700" rel="stylesheet">
	<link rel="icon" type="image/png" href="favicon/circular-arrows-2-16-245186.png">
</head>

<body>
	<section id="interactiveHeader">
		<!-- Interactive Header -->
		<div id="demo-1">
			<div class="content">
				<div id="large-header" class="large-header">
					<canvas id="demo-canvas"></canvas>
					<h1 id="main-title"><span class="thick"
							style="font-family:'Courier New', Courier, monospace;">M.F.M<br>Fazrin</span><br><span
							class="subtitle" style="font-family:Verdana, Geneva, Tahoma, sans-serif">Senior
							<br>Software<br>Engineer</span></h1>
				</div>
			</div>
		</div>
	</section> <!-- end of interactive header -->

	<nav class="navbar-fixed-top" style="opacity:.5" class="hidden-lg">
		<button type="button" id="navigationBarSection" aria-label="Menu">
			<ul class="nav navbar-nav navbar-left">
				<li><a href="#profileSection"><i class="ion-android-contact"></i></a></li>
				<li><a href="#projectsSection"><i class="ion-android-apps"></i></a></li>
				<li><a href="#highlightsSection"><i class="ion-ios-briefcase"></i></a></li>
				<li><a href="#contactSection"><i class="ion-android-call"></i></a></li>
			</ul>
		</button>
	</nav>

	<section id="aboutMeSection">
		<!-- About Me -->
	</section> <!-- end of about me -->

	<!--profile section begin here-->
	<section id="profileSection">
		<!-- Profile & Skills -->
		<div class="container-fluid">
			<div class="row">
				<h1>Profile</h1>
				<hr id="profileHeaderUnderline">
			</div>
			<div class="row">
				<div class="col-sm-3">
					<div id="profilePhotoContainer">
						<img src="img/profilePhoto.png" alt="Photo of M.F.M Fazrin">
						<div id="profileNamePositionDiv">
							<div>
								<p><span>Name: </span>M.F.M Fazrin</p>
							</div>
							<hr>
							<div>
								<p><span>Role: </span>Senior Software Development Specialist</p>
							</div>
						</div>
					</div>
				</div>
				<div class="col-sm-9">
					<h2>INTRO</h2>
					<hr id="profileIntroUnderline">
					<br>
					<p><span>I am Fazrin. </br>(MSc in Software Engineering @ <a
								href="https://www.kingston.ac.uk/">Kingston Metropolitan University)<span></span></a>
						</span><br><br>Working as a Senior Software Development Speci @<a href="https://quadrate.cc">Quadrate
							Tech Solutions</a> Constructing well-built high concurrent web applications, and I am
						passionate about making the end-user experience as seamless as possible.</p>
					<hr id="profileDivider">
					<p id="profileDescription">Please check out my <a href="#contactSection">career history</a> & <a
							href="#projectsSection">portfolio</a> section for more details, I was configuring and
						troubleshooting computer hardware & networking issues since 2005. Please refer to the <a
							href="#skills">section</a> to explore my familiar programming languages and tools that I
						have used<br><br>Examine my <a
							href="https://onedrive.live.com/embed?resid=E6D43771AF11C105%2122804&amp;authkey=%21AGMFWY_zWE80LBc&amp;em=2"
							target="_blank">resume</a></p>
							<a href="https://github.com/nirzaf/github-readme-stats" style="text-align:center;">
								<!-- Change the `github-readme-stats.anuraghazra1.vercel.app` to `github-readme-stats.vercel.app`  -->
								<img
									src="https://github-readme-stats.vercel.app/api/top-langs/?username=nirzaf&layout=compact&theme=material-palenight" />
							</a>
				</div>
			</div>
		</div>
	</section> <!-- end of profile -->


	<section id="skills">
		<!-- skills -->
		<div class="container-fluid">
			<h2>My Familiar Tools & Languages</h2>
			<hr id="profileDescription">
			<br>
			<div id="skillsRow" class="row">
				<img src="img/Net_Core.png">
				<img src="img/angularLogo.png">
				<img src="img/firebaseLogo.png">
				<img src="img/jQueryLogo.png">
				<img src="img/bootstrapLogo.png">
				<img src="img/javascriptLogo.png">
				<img src="img/html5Logo.png">
				<img src="img/css3Logo.png">
				<img src="img/typescript.png">
				<img src="img/githubLogo.png">
				<img src="img/gitlab.png">
				<img src="img/bitbucket.png">
				<img src="img/Azure.png">
				<img src="img/jira.png">
			</div>
		</div>
	</section>
	<!--End of skills sections-->
	<!--profile section ends here-->

	<section id="projectsSection">
		<!-- Projects -->
		<div class="container-fluid">
			<div class="row"><span>
					<h1></h1>
				</span></div>
			<div class="row">
				<p style="color:white;text-align: center;">My long journey accumulated lots of projects which cannot be
					listed all here<br> so please check out my complete list of projects in
					<a href="https://github.com/nirzaf">github</a> and <a href="https://gitlab.com/nirzaf">gitlab</a>
				</p>

				<a href="https://github.com/nirzaf/github-readme-stats">
					<img src="https://github-readme-stats.vercel.app/api?username=nirzaf&show_icons=true&include_all_commits=true&theme=material-palenight"
						alt="Fazrin's github stats" />
				</a>

			</div>
		</div>
	</section> <!-- end of projects -->


	<!-- <section id="highlightsSection">
	<h1>Career History</h1>
	<hr id="highlightsHeaderUnderline">
	<div class="container">
		<ul class="timeline" style="overflow-x: hidden">
			<li id="highlight1" class="timeline-inverted" style="display: none;">
				<div class="timeline-badge"><i class="ion-briefcase"></i></div>
				<div class="timeline-panel">
					<div class="timeline-heading">
						<h4 class="timeline-title"><a href="https://quadrate.cc/">Quadrate</a></h4>
						<p>July-2020 - Present</p>
						<p>Senior Full-Stack Engineer</p>
					</div>
					<div class="timeline-body">
						<img src="img/quadrate.png" alt="Sri Lankan based company which offers outsourcing solutions with virtualized work forces">
					</div>
				</div>
			</li>
			<li id="highlight2" style="display: none;">
				<div class="timeline-badge"><i class="ion-briefcase"></i></div>
				<div class="timeline-panel">
					<div class="timeline-heading">
						<h4 class="timeline-title"><a href="https://voigue.com">Voigue</a></h4>
						<p>Sept 2019 - Jun 2020</p>
						<p>Software Engineer</p>
					</div>
					<div class="timeline-body">
						<img src="img/voigue.png" alt="Sri Lankan based company which offers outsourcing solutions with virtualized work forces">
					</div>
				</div>
			</li>
			<li id="highlight3" class="timeline-inverted" style="display: none;">
				<div class="timeline-badge"><i class="ion-briefcase"></i></div>
				<div class="timeline-panel">
					<div class="timeline-heading">
						<h4 class="timeline-title"><a href="http://virtusa.com/">Virtusa</a></h4>
						<p>Jan 2019 - Jan 2019</p>
						<p>Associate Engineer</p>
					</div>
					<div class="timeline-body">
						<img src="img/1150Logo.png" alt="Virtusa Private Limited">
					</div>
				</div>
			</li>
			<li id="highlight4" style="display: none;">
				<div class="timeline-badge"><i class="ion-briefcase"></i></div>
				<div class="timeline-panel">
					<div class="timeline-heading">
						<h4 class="timeline-title"><a href="http://nemico.lk">Nemico Holdings</a></h4>
						<p>Jan 2016 - Dec 2018</p>
						<p>Software Developer</p>
					</div>
					<div class="timeline-body">
						<img src="img/nemico.jpg" alt="Qudrate Tech Solutions">
					</div>
				</div>
			</li>		
		</ul>
	</div>
</section> -->
	<section id="contactSection">
		<h3 style="text-align: center; color: antiquewhite;">Career History Profile</h3>
		<div style="position: relative; padding-bottom: calc(62.5% + 42px); height: 0;"><iframe frameborder="0"
				webkitallowfullscreen mozallowfullscreen allowfullscreen
				style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;"
				src="https://onedrive.live.com/embed?resid=E6D43771AF11C105%2122804&amp;authkey=%21AGMFWY_zWE80LBc&amp;em=2"></iframe>
		</div>
		<!-- Contact -->
		<div class="container-fluid">
			<div class="row">
				<h1>Contact</h1>
				<hr id="contactHeaderUnderline">
			</div>
			<h4 style="font-display:block;color:white">I am always reachable through WhatsApp </h4>
			<img src="img\logos\whatsapp.png"> <a
				href="https://api.whatsapp.com/send?phone=+94772049123&text=Hi, I contacted you Through your website."
				class="social-icon whatsapp">+94772049123</a>
			<h4 class="section-heading" style="font-display:block;color:white">Or Drop me an e-mail to</h4>
			<h4 class="section-heading" style="font-display:block"><a href="mailto:<EMAIL>">Email :
					<EMAIL></a></h4>
		</div>
		<div class="row">
			<a target="_blank" href="https://github.com/nirzaf"><i class="ion-social-github"></i></a>
			<a target="_blank" href="https://www.linkedin.com/in/mfmfazrin/"><i class="ion-social-linkedin"></i></a>
		</div>
	</section> <!-- end of contact -->

	<footer id="footerSection">
		<p>Copyright &copy; 2022 - <a href="https://nirzaf.github.io"> M.F.M Fazrin</a></p>
		<p>All trademarks and registered trademarks are the property of their respective owners.</p>
	</footer>
	<script type="text/javascript" src="js/TweenLite.min.js"></script>
	<script type="text/javascript" src="js/EasePack.min.js"></script>
	<script type="text/javascript" src="js/rAF.js"></script>
	<script type="text/javascript" src="js/interactiveHeader.js"></script>
	<script type="text/javascript" src="js/jquery-3.2.1.js"></script>
	<script type="text/javascript" src="js/jquery-ui.js"></script>
	<script type="text/javascript" src="js/bootstrap.js"></script>
	<script type="text/javascript" src="js/noframework.waypoints.js"></script>
	<script type="text/javascript" src="js/jquery.waypoints.js"></script>
	<script type="text/javascript" src="js/sticky.js"></script>
	<script type="text/javascript" src="js/main.js"></script>
	<script>
		document.addEventListener('DOMContentLoaded', function () {
			try {
				let app = firebase.app();
				let features = ['auth', 'database', 'messaging', 'storage'].filter(feature => typeof app[feature] ===
					'function');
				document.getElementById('load').innerHTML = `Firebase SDK loaded with ${features.join(', ')}`;
			} catch (e) {
				console.error(e);
				document.getElementById('load').innerHTML = 'Error loading the Firebase SDK, check the console.';
			}
		});
	</script>
</body>

</html>