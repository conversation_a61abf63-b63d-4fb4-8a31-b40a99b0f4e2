---
title: The New Extensions Everything Feature of C# 13
description: A detailed look at the new Extensions Everything feature in C# 13
pubDate: '2024-09-28'
image: >-
  https://ik.imagekit.io/quadrate/assets/img/dotnetevangelist/The_New_Extensions_Everything_Feature_Of_Csharp13.jpg?updatedAt=1746813307283
category: Csharp
tags: ['C#', 'Programming', '.Net']
---

Let's delve deeper into the new Extensions feature in C# 13 and explore how it can be a significant change for code readability and functionality. We'll look at each aspect of Extensions in detail and illustrate them with real-world examples and coding samples.

## Implicit vs. Explicit Extensions

Introduce this section with a brief description or an overview.

### Implicit Extensions

Implicit extensions in C# 13 allow you to extend a class without requiring any special syntax when using them. This means that you can use the extended functionality just like you would use any other member of the class.

```csharp
public class Product
{
    public decimal Price { get; set; }
}

public extension ProductExtensions extends Product
{
    public decimal CalculateSalesTax(decimal taxRate)
    {
        return Price * taxRate;
    }
}
```

### Explicit Extensions

Explicit extensions enable you to create projections or subcategories of the original class. This can be useful when you want to add conditional methods or properties based on the extended type.

```csharp
public extension TaxableProduct extends Product
{
    public decimal CalculateSalesTax(decimal taxRate)
    {
        return Price * taxRate;
    }
}
```

## Instance Members

One of the major improvements in C# 13's Extensions is the ability to include instance members (properties and methods) in addition to static methods. This allows for a more natural syntax when using the extended functionality.

```csharp
var product = new Product { Price = 100 };
var salesTax = product.CalculateSalesTax(0.05m);
```

## Improved Code Organization

By combining implicit and explicit extensions in a single class, you can organize the extension logic alongside the code that uses it. This enhances readability and maintainability by keeping related code together.

## Conclusion

The Extensions feature in C# 13 represents a significant addition to the language that promises to improve code readability, maintainability, and expressiveness. By allowing developers to add functionality to existing classes without modifying their original code, Extensions promote code reuse and reduce the need to modify original class definitions.

Thanks to Nick Chapsas

The New Extensions EVERYTHING Feature of C# 13! — YouTubeThe New Extensions EVERYTHING Feature of C# 13! — YouTube
