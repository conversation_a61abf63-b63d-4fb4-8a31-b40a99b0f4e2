---
title: My Profile
pubDate: '2022-04-30'
image: >-
  https://ik.imagekit.io/quadrate/assets/img/dotnetevangelist/my_profile_pic.jpg?updatedAt=1746813300842
tags: ['<PERSON>', '<PERSON><PERSON><PERSON>', 'Farook', 'Software', 'Engineer']
description: >-
  Professional profile of <PERSON><PERSON> <PERSON><PERSON>, a Software Development Specialist with
  extensive experience in .NET, Azure, and full-stack development.
draft: true
---

import { Image } from 'astro:assets';

# My Profile

**M.F.M Fazrin (MSc – SE) - 📌 Doha, Qatar.**

📞[+97433253203](tel:+97433253203)

📱 [+94772049123](https://wa.me/94772049123)

✉️ [<EMAIL>](mailto:<EMAIL>)

🤵🏻 [https://nirzaf.github.io](https://nirzaf.github.io/)

🐱‍👤[https://github.com/nirzaf](https://github.com/nirzaf)

💼[https://linkedin.com/in/mfmfazrin](https://linkedin.com/in/mfmfazrin)

📝[https://dotnetevangelist.net](https://dotnetevangelist.net/)

⭐[Highest-ranked developer in Qatar](https://stardev.io/top/developers/all/in/qatar)

<a href="https://stardev.io/developers/nirzaf">
  <Image src="https://stardev.io/developers/nirzaf/badge/languages/global.svg" alt="nirzaf language badge" width={200} height={40} />
</a>

---

## Professional Experience

### Software Development Specialist (July-2022) @ Primary Health Care Corporation (Qatar)
- Project: Nar'aakom Mobile Application (Backend with Azure Functions with C#)
- Working on backend .NET services to migrate REST API to GraphQL with Azure Active Directory.
- Optimizing the query performance using Azure Redis distributed caching.
- Migrating data from SQL server to FHIR (NoSql type) database
- Developing an Open API interface to allow third-party services to integrate with the Nar'aakom application
- Unit testing and Integration testing with [xUnit.net](http://xunit.net/) and Moq framework.
- Tools & Technologies Using - .NET 6 & above, C# 10 & above, Azure Functions, Visual Studio 2022, Jet brains Rider, [xUnit.net](http://xunit.net/), ReSharper, SQL Server 2022, CI CD pipeline in Azure DevOps.

### Senior Full-stack Engineer (July-2020 – June-2022) @ Quadrate Tech Solutions Private Limited
- Project: Hotel ERP (SaaS-based ERP Solution for hotels)
- Working in the administration module, which handles authentication & authorization along with primary configurations of each property's subscribed modules
- Successfully deployed a mail service and SMS gateway using Azure Functions and Logic Apps to communicate with users and third parties.
- Synchronize legacy data from SQL Server to Cosmos Database via SQL API to increase the scalability of the system
- Contributing to user interface development using Angular
- Microservices integration using Azure Service Bus & RabbitMQ (Pub-Sub pattern)
- Configure CI-CD using Azure DevOps pipelines
- Unit Testing with [xUnit.net](http://xunit.net/) (Mock, [Faker.Net](http://faker.net/), Stubs)
- Tools & Technologies Using - .NET Core 3.1 & above, C# 9 & above, Angular 12, Azure Functions, Azure Logic Apps, Cosmos Db, VS 2022, VS Code, Jet brains Rider, [xUnit.net](http://xunit.net/), ReSharper, Azure Service Bus, RabbitMQ, SQL Server 2022, CI CD pipeline in Azure DevOps.

### Dot NET Engineer (Nov 2019 - June 2020) @ Voigue Private Limited
- Project: SmartPABX - Cloud-Based Phone System
- .NET Engineer in WPF-based Project for an Australian Telecommunication Company.
- Developed Backend API with .NET Core and Updated the existing PABX legacy system to the latest version.
- User interface optimized completely by converting WinForms to WPF and enabled Dynamic User interface functions such as call forward, group calls, call parking and many other features.
- Tools & Technologies Used - .NET Core 2.1, AsterNET, WPF, C# (7), REST API, XAML, PABX Asterisk, MariaDB, Apache Server, JSON, Visual Studio 2019.

### Associate Full Stack-Engineer (01/2019 - 10/2019) @ Virtusa Private Limited
- McDonald's - Workforce Operations Labor Forecasting System
- Developed a web application to forecast labour requirements for various restaurant configurations based on frequently migrated sales data to improve labour allocations. The Decision-making process improved by five minutes versus the previous six months and zeroed the manager person-hours needed to input and cleanse the sales data.
- Implementation of new features. Optimizations. Bug prioritizing and bug fixing and producing detailed technical documentation.
- 10/10 client scorecards from the project's inception till production were maintained.
- Technologies Used - Angular 6, Bootstrap 4.2, .NET Core 2.0, Web API, EF Core 2.0, AutoMapper, SQL Server 2017, Azure Web Jobs, Azure Blob and File Storage, Azure Insights, Azure Redis Cache, Visual Studio 2017, Azure CI/CD and OpenXml.

### Software Developer (Jan 2016 - Dec 2018) @ Nemico Holdings
- Inventory Management System for large fashion retailers
- POS integration to keep updating of inventory database with day-to-day transaction
- Successfully fixed bugs as per the Agile methodology implemented new user privileges, retrofitted the legacy product management tool, and implemented a cloud-based enterprise inventory management developed in [ASP.NET](http://asp.net/) (4.8)
- Successfully migrated the complete reporting system from Crystal Reports to iTextSharp to improve the performance and save cost from the enterprise reporting tool
- Tools & Technologies Used - C#.NET, Microsoft Visual Studio 2015, Microsoft SQL Server, 2012, [ASP.NET](http://asp.net/), iTextSharp, Crystal Report for Web.

### Backend Developer (06/2012 - 07/2015) @ Olayan (Saudi Arabia)
- Centralize the online order-taking process and increase the field sales force productivity and business insights with real-time visibility of all operations such as delivery, sales, marketing, and management.
- Improved the UX by developing a real-time notification feature using SignalR
- Introduced PDF.js instead of Box View and enhanced the document viewing functionality, reducing the response time by a minute average per document and eliminating license costs.
- Tools & Technologies Used - .NET Framework 4.0, SignalR, [ASP.NET](http://asp.net/) Web API, IIS, PDF.js, Microsoft .NET, ASP Web API, Microsoft Visual Studio 2012, Microsoft SQL Server 2008 R2

### Junior Developer (08/2009 - 04/2012) @ QTS Private Limited
- LECO Energy - Field Report Writer/Total Maintenance Service
- Participated in the application development, which allowed field service engineers to capture and record information on site regarding power turbines and generators during their maintenance visits and synchronized all data to a centralized server for reporting purposes when connecting to the network.
- Successfully reverse-engineered the underlying implementation of Office Info Path 2003, which was heavily versed in XML
- Developed add-ons while ensuring performance was not compromised and license constraints were achieved.
- Optimized the team's efficiency while working on feature implementations and bug fixes.

---

## Education & Other Qualifications
- MSc in Software Engineering @ Kingston University
- BE in Software Engineering @ London Metropolitan University
- IELTS (Academic) 7.5 Average (May – 2021)
---
