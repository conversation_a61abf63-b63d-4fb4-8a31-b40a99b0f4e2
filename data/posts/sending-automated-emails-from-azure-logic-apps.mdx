---
title: Sending Emails From Azure Logic Apps Service Bus
description: >-
  Learn how to send emails from Azure Logic Apps triggered by Azure Service Bus
  messages.
pubDate: '2024-02-28'
image: >-
  https://ik.imagekit.io/quadrate/assets/img/dotnetevangelist/Azure_Logic_Apps_Email.jpg?updatedAt=1746813307747
category: Azure
tags: ['Azure', 'Logic Apps', 'Service Bus', 'Email']
---

**Automate sending emails based on Azure Service Bus messages using Azure Logic Apps.**

## Introduction

This blog post demonstrates how to configure Azure Logic Apps to send emails triggered by messages received on an Azure Service Bus topic subscription.  This is a powerful way to automate notifications and other email-driven processes based on events captured in your Service Bus.

## Prerequisites

- Azure Subscription
- Email provider (Such as SendGrid, MailGun, Postmark, etc.)

## Background

### What is Azure Logic Apps?

Azure Logic Apps is a cloud-based service for creating and running automated workflows that integrate apps, data, systems, and services across enterprises or organizations. It simplifies the design and development of scalable solutions for integration scenarios.

### What is Azure Service Bus?

Azure Service Bus is a fully managed message broker used for reliable asynchronous communication between applications and services. It decouples systems and enables secure data transfer using messages.

### What is an Email Provider?

An email provider offers email hosting services. It's distinct from an email client (like Outlook or Gmail), which is the software used to access email.  You'll need an email provider to send emails from your Logic App.

## Sending Emails from Logic Apps

Sending emails through Azure Logic Apps simplifies fulfilling business requirements, especially when triggered by Azure Service Bus.

The Azure Service Bus message hierarchy is:

- Service Bus Namespace
- Topic
- Subscriptions

## Implementation Steps

1. **Create a Resource Group and a Logic App:**  Start by creating a resource group and a new Logic App within the Azure portal.

2. **Configure the Trigger:** In the Logic App Designer, select the "When a message is received in a topic subscription" trigger.
content_copy
Use code with caution.
Mdx

When a message is available in a topic subscription

<center>

    ![Logic App Trigger](https://ik.imagekit.io/quadrate/assets/img/dotnetevangelist/Azure_Logic_Apps_Email.jpg?updatedAt=1746813307747)

</center>


Provide the connection string for your Service Bus, the topic name, and the subscription name.

**Sample ARM template for the trigger:**

```json
{
    "inputs": {
        "parameters": {
            "topicName": "your-topic-name-here",
            "subscriptionName": "your-subscription-name-here"
        },
        "serviceProviderConfiguration": {
            "connectionName": "serviceBus",
            "operationId": "receiveTopicMessages",
            "serviceProviderId": "/serviceProviders/serviceBus"
        }
    },
    "splitOn": "@triggerOutputs()?['body']"
}

```

### Create an HTTP Action

Add an HTTP action to send the email.

- **Method:** POST
- **URI:** Your email provider's POST API endpoint URI
- **Headers:** Include necessary authentication tokens and content-type headers.
- **Body:** Construct the email body using data from the Service Bus message. The following example shows how to access properties from the message:

#### Sample ARM template for the HTTP action:

```json
{
    "inputs": {
        "method": "POST",
        "uri": "your-email-service-provider-API-endpoint",
        "headers": {
            "token": "your-authentication-token",
            "content-type": "application/json"
        },
        "body": {
            "From": "@triggerOutputs()?['body']?['UserProperties/from']",
            "HtmlBody": "@triggerOutputs()?['body']?['UserProperties/htmlBody']",
            "ReplyTo": "@triggerOutputs()?['body']?['replyTo']",
            "Subject": "@triggerOutputs()?['body']?['UserProperties/subject']",
            "To": "@triggerOutputs()?['body']?['to']"
        }
    }
}
```
**Save the Workflow:** Save the Logic App workflow. It will now trigger every time a message is received on the specified Service Bus topic subscription.
