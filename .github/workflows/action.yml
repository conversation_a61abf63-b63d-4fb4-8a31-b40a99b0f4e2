name: C<PERSON> and Deploy

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]
  workflow_dispatch:

permissions:
  contents: write
  pages: write
  id-token: write

concurrency:
  group: "pages"
  cancel-in-progress: false

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: "20.x"

      
      - name: Install dependencies
        run: npm ci

      - name: Build with Next.js
        run: |
          npm run build
          npm run generate-search-index
        env:
          NEXT_TELEMETRY_DISABLED: 1
          NODE_ENV: production
          # For GitHub Pages, we need to set the base path
          NEXT_PUBLIC_BASE_PATH: /nirzaf.github.io

      - name: Create backup branch
        if: success()
        run: |
          timestamp=$(date +"%Y%m%d%H%M%S")
          git checkout -b master-backup-$timestamp
          git push https://$<EMAIL>/${{ github.repository }}.git master-backup-$timestamp
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3
        with:
          path: ./out

  deploy:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
