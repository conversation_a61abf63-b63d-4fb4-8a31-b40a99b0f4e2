// ***********************************************************
// This example support/e2e.ts is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import './commands'

// Suppress uncaught exceptions
Cypress.on('uncaught:exception', (err, runnable) => {
  // returning false here prevents <PERSON><PERSON> from failing the test
  return false;
});

// We'll use a different approach for checking images
// This command was causing TypeScript errors
