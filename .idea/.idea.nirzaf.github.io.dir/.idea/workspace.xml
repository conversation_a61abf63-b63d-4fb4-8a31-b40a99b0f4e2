<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="95dc1786-70e0-4fbb-98a6-ae0f8e769326" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/.idea/.idea.nirzaf.github.io.dir/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/.idea.nirzaf.github.io.dir/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.vs/VSWorkspaceState.json" beforeDir="false" afterPath="$PROJECT_DIR$/.vs/VSWorkspaceState.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.vs/nirzaf.github.io/v17/.suo" beforeDir="false" afterPath="$PROJECT_DIR$/.vs/nirzaf.github.io/v17/.suo" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.vs/slnx.sqlite" beforeDir="false" afterPath="$PROJECT_DIR$/.vs/slnx.sqlite" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectId" id="21d940gU940IZc120h7i9L5yR5w" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ASKED_SHARE_PROJECT_CONFIGURATION_FILES": "true",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "WebServerToolWindowFactoryState": "false",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="index.html" type="JavascriptDebugType" temporary="true" nameIsGenerated="true" uri="http://localhost:63342/nirzaf.github.io/index.html" useBuiltInWebServerPort="true">
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="JavaScript Debug.index.html" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="95dc1786-70e0-4fbb-98a6-ae0f8e769326" name="Changes" comment="" />
      <created>1638259906075</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1638259906075</updated>
      <workItem from="1638259913446" duration="509000" />
      <workItem from="1657646463039" duration="1893000" />
    </task>
    <task id="LOCAL-00001" summary="override changes">
      <created>1657647700606</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1657647700606</updated>
    </task>
    <task id="LOCAL-00002" summary="hghgh">
      <created>1657647818303</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1657647818303</updated>
    </task>
    <task id="LOCAL-00003" summary="&#9;&#9;&#9;&#9;listed all here&lt;br&gt; so please check out my complete list of projects in">
      <created>1657648134539</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1657648134539</updated>
    </task>
    <option name="localTasksCounter" value="4" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="ADD_EXTERNAL_FILES_SILENTLY" value="true" />
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
    <MESSAGE value="override changes" />
    <MESSAGE value="hghgh" />
    <MESSAGE value="&#9;&#9;&#9;&#9;listed all here&lt;br&gt; so please check out my complete list of projects in" />
    <option name="LAST_COMMIT_MESSAGE" value="&#9;&#9;&#9;&#9;listed all here&lt;br&gt; so please check out my complete list of projects in" />
  </component>
</project>