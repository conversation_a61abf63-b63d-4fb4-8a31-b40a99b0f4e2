<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="bc0ef056-2531-4deb-a799-cb519baa3441" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/.idea/projectSettingsUpdater.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/projectSettingsUpdater.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="JavaScript File" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="master" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="HARD" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;nirzaf&quot;
  }
}</component>
  <component name="GitToolBoxStore">
    <option name="recentBranches">
      <RecentBranches>
        <option name="branchesForRepo">
          <list>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="fix-yaml-frontmatter" />
                    <option name="lastUsedInstant" value="1727855313" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="master" />
                    <option name="lastUsedInstant" value="**********" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$" />
            </RecentBranchesForRepo>
          </list>
        </option>
      </RecentBranches>
    </option>
  </component>
  <component name="GithubDefaultAccount">
    <option name="defaultAccountId" value="5ed92374-9d5c-4123-b852-5f0a5c3534cf" />
  </component>
  <component name="GithubPullRequestsUISettings"><![CDATA[{
  "selectedUrlAndAccountId": {
    "url": "https://github.com/mmfazrin-phcc-gov/nirzaf.github.io.git",
    "accountId": "380f5c4c-be84-48cf-a0da-a2f181072b0a"
  }
}]]></component>
  <component name="HighlightingSettingsPerFile">
    <setting file="mock://C:/Users/<USER>/RiderProjects/dotnetblogs/package-lock.json" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/package-lock.json" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock://C:/Users/<USER>/RiderProjects/dotnetblogs/package-lock.json" root0="FORCE_HIGHLIGHTING" />
    <setting file="mock://C:/Users/<USER>/RiderProjects/dotnetblogs/package.json" root0="FORCE_HIGHLIGHTING" />
    <setting file="mock://C:/Users/<USER>/RiderProjects/dotnetblogs/package.json" root0="FORCE_HIGHLIGHTING" />
    <setting file="mock://C:/Users/<USER>/RiderProjects/dotnetblogs/package.json" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="MetaFilesCheckinStateConfiguration" checkMetaFiles="true" />
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2mqFYlFBUR6YdtMhJRH0MJfzyOE" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.CodyAccountHistoryMigration&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.CodyAccountsIdsRefresh&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.CodyAssignOrphanedChatsToActiveAccount&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.CodyConvertUrlToCodebaseName&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.CodyHistoryLlmMigration&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.CodyMigrateChatHistory-v2&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.CodyProjectSettingsMigration&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ToggleCodyToolWindowAfterMigration&quot;: &quot;true&quot;,
    &quot;com.codeium.enabled&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;npm.build.executor&quot;: &quot;Run&quot;,
    &quot;npm.preview.executor&quot;: &quot;Run&quot;,
    &quot;npm.start.executor&quot;: &quot;Run&quot;,
    &quot;prettierjs.PrettierConfiguration.Package&quot;: &quot;C:\\Users\\<USER>\\RiderProjects\\dotnetblogs\\node_modules\\prettier&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;ts.external.directory.path&quot;: &quot;C:\\Users\\<USER>\\RiderProjects\\dotnetblogs\\node_modules\\typescript\\lib&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager" selected="npm.start">
    <configuration name="build" type="js.build_tools.npm" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="build" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="preview" type="js.build_tools.npm" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="preview" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="start" type="js.build_tools.npm" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="start" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="npm.start" />
      <item itemvalue="npm.build" />
      <item itemvalue="npm.preview" />
    </list>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-5c90d61e3bab-JavaScript-WS-242.23726.96" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="bc0ef056-2531-4deb-a799-cb519baa3441" name="Changes" comment="" />
      <created>1727796251534</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1727796251534</updated>
      <workItem from="1727796255657" duration="3209000" />
      <workItem from="1727810399326" duration="2803000" />
      <workItem from="1727853801941" duration="226000" />
      <workItem from="1727854213385" duration="2506000" />
      <workItem from="1727860356589" duration="590000" />
      <workItem from="1727958533772" duration="6449000" />
      <workItem from="1728032477938" duration="2437000" />
      <workItem from="1728247158468" duration="451000" />
      <workItem from="1728308912712" duration="284000" />
      <workItem from="1728324549706" duration="5065000" />
      <workItem from="1728407638847" duration="199000" />
      <workItem from="1728407889552" duration="5177000" />
      <workItem from="1728667601049" duration="137000" />
      <workItem from="1728667848920" duration="13893000" />
      <workItem from="1728722791060" duration="2748000" />
      <workItem from="1728922227001" duration="61000" />
      <workItem from="1728924048656" duration="376000" />
      <workItem from="1730050518681" duration="1867000" />
      <workItem from="1733816100357" duration="191000" />
    </task>
    <task id="LOCAL-00020" summary="```&#10;feat(blog): Update publication date in profile&#10;&#10;Update publication date in profile.&#10;```">
      <option name="closed" value="true" />
      <created>1727856257642</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1727856257642</updated>
    </task>
    <task id="LOCAL-00021" summary="feat(pages/PostCard): refactor post card component layout and media queries">
      <option name="closed" value="true" />
      <created>1727875053026</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1727875053026</updated>
    </task>
    <task id="LOCAL-00022" summary="feat(components): update social media icons and links">
      <option name="closed" value="true" />
      <created>1727875503003</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1727875503003</updated>
    </task>
    <task id="LOCAL-00023" summary="feat: Update title of Dotnet Blogs to DotNet Blogs in TitlePage component">
      <option name="closed" value="true" />
      <created>1727876681229</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1727876681229</updated>
    </task>
    <task id="LOCAL-00024" summary="feat: Update TwitterIcon SVG with new design">
      <option name="closed" value="true" />
      <created>1727885959216</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1727885959216</updated>
    </task>
    <task id="LOCAL-00025" summary="feat: add about link to header">
      <option name="closed" value="true" />
      <created>1727886655050</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1727886655050</updated>
    </task>
    <task id="LOCAL-00026" summary="refactor(icons): Remove unused xmlns:xlink from TwitterColorIcon">
      <option name="closed" value="true" />
      <created>1727886699745</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1727886699745</updated>
    </task>
    <task id="LOCAL-00027" summary="feat: implement responsive resume page with detailed information">
      <option name="closed" value="true" />
      <created>1727889067140</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1727889067140</updated>
    </task>
    <task id="LOCAL-00028" summary="feat: Update resume with formatted project details">
      <option name="closed" value="true" />
      <created>1727889659538</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1727889659538</updated>
    </task>
    <task id="LOCAL-00029" summary="refactor(pages): remove unnecessary line breaks in HTML">
      <option name="closed" value="true" />
      <created>1727889807833</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1727889807833</updated>
    </task>
    <task id="LOCAL-00030" summary="refactor(pages): update CSS inheritance">
      <option name="closed" value="true" />
      <created>1727891324032</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1727891324032</updated>
    </task>
    <task id="LOCAL-00031" summary="feat(Footer): Refactor footer content layout">
      <option name="closed" value="true" />
      <created>1727891724897</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1727891724897</updated>
    </task>
    <task id="LOCAL-00032" summary="fix: correct TitlePage title case">
      <option name="closed" value="true" />
      <created>1727891802854</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1727891802854</updated>
    </task>
    <task id="LOCAL-00033" summary="fix(Title.astro): Adjusted font size of title">
      <option name="closed" value="true" />
      <created>1727891869482</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1727891869482</updated>
    </task>
    <task id="LOCAL-00034" summary="Fix: Correct TitlePage title case and adjust title font size (master)&#10;&#10;This commit includes the following changes:&#10;&#10;- Corrects the title case in the TitlePage component.&#10;- Adjusts the font size of the title in Title.astro.&#10;- Adds a new  `jsLibraryMappings.xml` file to configure &#10;  Javascript libraries.&#10;- Updates the `Project_Default.xml` file to include &#10;  `object-contain` as a known CSS property.&#10;- Refactors the workspace settings in `.idea/workspace.xml`,&#10;  updating the default account ID and other settings.">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00035" summary="feat(README): Update Spanish README with project details and remove Vercel and Netlify badges">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00036" summary="feat: Remove Demo section and contributor list from README">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00037" summary="feat: Remove obsolete roadmap items from README">
      <option name="closed" value="true" />
      <created>1728032740780</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1728032740780</updated>
    </task>
    <task id="LOCAL-00038" summary="feat: Update README with new project details and remove unused content">
      <option name="closed" value="true" />
      <created>1728032906973</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1728032906973</updated>
    </task>
    <task id="LOCAL-00039" summary="docs: Remove profile header in README">
      <option name="closed" value="true" />
      <created>1728032957894</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1728032957894</updated>
    </task>
    <task id="LOCAL-00040" summary="feat(README.md): Add 'Buy Me A Coffee' link">
      <option name="closed" value="true" />
      <created>1728033795886</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>1728033795886</updated>
    </task>
    <task id="LOCAL-00041" summary="feat: Add Buy Me A Coffee button to about page (#)&#10;&#10;Added a &quot;Buy Me A Coffee&quot; button to the about page to &#10;support the creation of more content and improve the blog.">
      <option name="closed" value="true" />
      <created>1728034178889</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1728034178889</updated>
    </task>
    <task id="LOCAL-00042" summary="feat: Add default theme in updateTheme function">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00043" summary="feat: Add blog post about MFA with Microsoft Authenticator (master)&#10;&#10;Added a new blog post explaining how to enable multi-factor &#10;authentication using the Microsoft Authenticator app for &#10;Office 365 accounts.  Also updated the categories data to &#10;include &quot;O365&quot; and added bootstrap and react-bootstrap &#10;packages.">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00043" />
      <option name="presentableId" value="LOCAL-00043" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00044" summary="feat: Add 'Categories' feature to the website">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00044" />
      <option name="presentableId" value="LOCAL-00044" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00045" summary="feat: Add 'Categories' feature to the website">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00045" />
      <option name="presentableId" value="LOCAL-00045" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00046" summary="feat: Add 'Categories' feature to the website (master)&#10;&#10;This commit adds the">
      <option name="closed" value="true" />
      <created>1728410407162</created>
      <option name="number" value="00046" />
      <option name="presentableId" value="LOCAL-00046" />
      <option name="project" value="LOCAL" />
      <updated>1728410407162</updated>
    </task>
    <task id="LOCAL-00047" summary="feat: Add freelancer profile section (master)&#10;&#10;Adds a freelancer profile section to the about page,&#10;including contact information and a link to hire the author&#10;on Freelancer.com.&#10;&#10;The section is styled to fit with the rest of the page&#10;and is responsive to different screen sizes.">
      <option name="closed" value="true" />
      <created>1728413991494</created>
      <option name="number" value="00047" />
      <option name="presentableId" value="LOCAL-00047" />
      <option name="project" value="LOCAL" />
      <updated>1728413991494</updated>
    </task>
    <task id="LOCAL-00048" summary="feat: Add freelancer profile section (master)&#10;&#10;Adds a freelancer profile section to the about page,&#10;including contact information and a link to hire the author&#10;on Freelancer.com.&#10;&#10;The section is styled to fit with the rest of the page&#10;and is responsive to different screen sizes.">
      <option name="closed" value="true" />
      <created>1728414513849</created>
      <option name="number" value="00048" />
      <option name="presentableId" value="LOCAL-00048" />
      <option name="project" value="LOCAL" />
      <updated>1728414513849</updated>
    </task>
    <task id="LOCAL-00049" summary="feat(about): add freelancer profile section">
      <option name="closed" value="true" />
      <created>1728415227043</created>
      <option name="number" value="00049" />
      <option name="presentableId" value="LOCAL-00049" />
      <option name="project" value="LOCAL" />
      <updated>1728415227043</updated>
    </task>
    <task id="LOCAL-00050" summary="fix(about.astro): Remove unnecessary closing tag">
      <option name="closed" value="true" />
      <created>1728415251259</created>
      <option name="number" value="00050" />
      <option name="presentableId" value="LOCAL-00050" />
      <option name="project" value="LOCAL" />
      <updated>1728415251260</updated>
    </task>
    <task id="LOCAL-00051" summary="fix: fix tag formatting in blog post (master)&#10;&#10;Improved formatting of tags in the blog post.  Removed &#10;unnecessary commas.">
      <option name="closed" value="true" />
      <created>1728660324519</created>
      <option name="number" value="00051" />
      <option name="presentableId" value="LOCAL-00051" />
      <option name="project" value="LOCAL" />
      <updated>1728660324519</updated>
    </task>
    <task id="LOCAL-00052" summary="feat: updates blog site and moves blog-template.mdx to src/content/blog">
      <option name="closed" value="true" />
      <created>1728670369463</created>
      <option name="number" value="00052" />
      <option name="presentableId" value="LOCAL-00052" />
      <option name="project" value="LOCAL" />
      <updated>1728670369464</updated>
    </task>
    <task id="LOCAL-00053" summary="```&#10;chore: update dependencies (master)&#10;&#10;Update Astro and related dependencies to their latest &#10;versions.  This includes updates to @astrojs/mdx, &#10;@astrojs/rss, @astrojs/sitemap, @astrojs/tailwind, &#10;and other packages.&#10;```">
      <option name="closed" value="true" />
      <created>1728674329656</created>
      <option name="number" value="00053" />
      <option name="presentableId" value="LOCAL-00053" />
      <option name="project" value="LOCAL" />
      <updated>1728674329656</updated>
    </task>
    <task id="LOCAL-00054" summary="feat(backup): add automated backup branch creation">
      <option name="closed" value="true" />
      <created>1728675437849</created>
      <option name="number" value="00054" />
      <option name="presentableId" value="LOCAL-00054" />
      <option name="project" value="LOCAL" />
      <updated>1728675437849</updated>
    </task>
    <task id="LOCAL-00055" summary="feat: Update config for AstroJS site">
      <option name="closed" value="true" />
      <created>1728675648304</created>
      <option name="number" value="00055" />
      <option name="presentableId" value="LOCAL-00055" />
      <option name="project" value="LOCAL" />
      <updated>1728675648304</updated>
    </task>
    <task id="LOCAL-00056" summary="feat: remove unnecessary comment in astro.config.mjs (#123)&#10;&#10;This commit removes a now unnecessary comment from &#10;the `astro.config.mjs` file.  The comment was &#10;unnecessary and the configuration is clear without it.">
      <option name="closed" value="true" />
      <created>1728675666326</created>
      <option name="number" value="00056" />
      <option name="presentableId" value="LOCAL-00056" />
      <option name="project" value="LOCAL" />
      <updated>1728675666326</updated>
    </task>
    <task id="LOCAL-00057" summary="feat(ops): Move backup branch creation to setup Node action">
      <option name="closed" value="true" />
      <created>1728675815710</created>
      <option name="number" value="00057" />
      <option name="presentableId" value="LOCAL-00057" />
      <option name="project" value="LOCAL" />
      <updated>1728675815711</updated>
    </task>
    <task id="LOCAL-00058" summary="feat: refactor Search component structure">
      <option name="closed" value="true" />
      <created>1728677452237</created>
      <option name="number" value="00058" />
      <option name="presentableId" value="LOCAL-00058" />
      <option name="project" value="LOCAL" />
      <updated>1728677452237</updated>
    </task>
    <task id="LOCAL-00059" summary="feat: Refactor Search component for better readability and maintainability">
      <option name="closed" value="true" />
      <created>1728679769615</created>
      <option name="number" value="00059" />
      <option name="presentableId" value="LOCAL-00059" />
      <option name="project" value="LOCAL" />
      <updated>1728679769615</updated>
    </task>
    <task id="LOCAL-00060" summary="Fix: Re-attach event listeners after navigation (master)&#10;&#10;Improved the `astro:page-load` event listener to &#10;re-attach event listeners after navigation, &#10;ensuring proper functionality.  Added error &#10;handling and cleared existing search content &#10;before initializing PagefindUI.  Added a placeholder&#10;for `openModal` function.">
      <option name="closed" value="true" />
      <created>1728680480628</created>
      <option name="number" value="00060" />
      <option name="presentableId" value="LOCAL-00060" />
      <option name="project" value="LOCAL" />
      <updated>1728680480628</updated>
    </task>
    <task id="LOCAL-00061" summary="image updated">
      <option name="closed" value="true" />
      <created>1728681997726</created>
      <option name="number" value="00061" />
      <option name="presentableId" value="LOCAL-00061" />
      <option name="project" value="LOCAL" />
      <updated>1728681997726</updated>
    </task>
    <task id="LOCAL-00062" summary="Updates sitemap URL to dotnetevangelist.com.">
      <option name="closed" value="true" />
      <created>1728682416842</created>
      <option name="number" value="00062" />
      <option name="presentableId" value="LOCAL-00062" />
      <option name="project" value="LOCAL" />
      <updated>1728682416842</updated>
    </task>
    <task id="LOCAL-00063" summary="feat: Add Google AdSense script to BaseHead (master)&#10;&#10;Adds Google AdSense script to the `&lt;head&gt;` section for &#10;monetization purposes.  The script is added asynchronously &#10;to avoid blocking page rendering.">
      <option name="closed" value="true" />
      <created>1728687655184</created>
      <option name="number" value="00063" />
      <option name="presentableId" value="LOCAL-00063" />
      <option name="project" value="LOCAL" />
      <updated>1728687655184</updated>
    </task>
    <task id="LOCAL-00064" summary="feat: Add Google AdSense to header and index page&#10;&#10;Adds Google AdSense script to the header and index &#10;page for monetization.  The script is included &#10;asynchronously to avoid blocking page rendering.">
      <option name="closed" value="true" />
      <created>1728688016160</created>
      <option name="number" value="00064" />
      <option name="presentableId" value="LOCAL-00064" />
      <option name="project" value="LOCAL" />
      <updated>1728688016160</updated>
    </task>
    <task id="LOCAL-00065" summary="```&#10;feat: Update package-lock.json (master)&#10;&#10;Update various packages to their latest versions.  This &#10;includes updates to @babel packages, @rollup packages, &#10;@types/node, and several other dependencies.  No &#10;functional changes are expected.&#10;```">
      <option name="closed" value="true" />
      <created>1728688549231</created>
      <option name="number" value="00065" />
      <option name="presentableId" value="LOCAL-00065" />
      <option name="project" value="LOCAL" />
      <updated>1728688549231</updated>
    </task>
    <task id="LOCAL-00066" summary="feat: Add blog post about Repository Pattern in .NET&#10;&#10;This commit adds a new blog post explaining the &#10;Repository Pattern in .NET, including code &#10;examples and best practices.  The post covers &#10;defining interfaces, implementing repositories, &#10;and using dependency injection.">
      <option name="closed" value="true" />
      <created>1728724620978</created>
      <option name="number" value="00066" />
      <option name="presentableId" value="LOCAL-00066" />
      <option name="project" value="LOCAL" />
      <updated>1728724620979</updated>
    </task>
    <task id="LOCAL-00067" summary="feat: Update 'implementing-the-repository-pattern-in-dot-net-a-game-changer-for-clean-code' blog with improved structure and added code snippets.">
      <option name="closed" value="true" />
      <created>1728725078544</created>
      <option name="number" value="00067" />
      <option name="presentableId" value="LOCAL-00067" />
      <option name="project" value="LOCAL" />
      <updated>1728725078544</updated>
    </task>
    <task id="LOCAL-00068" summary="Update footer styling, add Google Ads script, set dynamic title, and adjust post display settings">
      <option name="closed" value="true" />
      <created>1728924404692</created>
      <option name="number" value="00068" />
      <option name="presentableId" value="LOCAL-00068" />
      <option name="project" value="LOCAL" />
      <updated>1728924404692</updated>
    </task>
    <option name="localTasksCounter" value="69" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityCheckinConfiguration" checkUnsavedScenes="true" />
  <component name="UnityProjectConfiguration" hasMinimizedUI="false" />
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
    <MESSAGE value="feat: Add default theme in updateTheme function" />
    <MESSAGE value="feat: Add blog post about MFA with Microsoft Authenticator (master)&#10;&#10;Added a new blog post explaining how to enable multi-factor &#10;authentication using the Microsoft Authenticator app for &#10;Office 365 accounts.  Also updated the categories data to &#10;include &quot;O365&quot; and added bootstrap and react-bootstrap &#10;packages." />
    <MESSAGE value="feat: Add 'Categories' feature to the website" />
    <MESSAGE value="feat: Add 'Categories' feature to the website (master)&#10;&#10;This commit adds the" />
    <MESSAGE value="feat: Add freelancer profile section (master)&#10;&#10;Adds a freelancer profile section to the about page,&#10;including contact information and a link to hire the author&#10;on Freelancer.com.&#10;&#10;The section is styled to fit with the rest of the page&#10;and is responsive to different screen sizes." />
    <MESSAGE value="feat(about): add freelancer profile section" />
    <MESSAGE value="fix(about.astro): Remove unnecessary closing tag" />
    <MESSAGE value="fix: fix tag formatting in blog post (master)&#10;&#10;Improved formatting of tags in the blog post.  Removed &#10;unnecessary commas." />
    <MESSAGE value="feat: updates blog site and moves blog-template.mdx to src/content/blog" />
    <MESSAGE value="```&#10;chore: update dependencies (master)&#10;&#10;Update Astro and related dependencies to their latest &#10;versions.  This includes updates to @astrojs/mdx, &#10;@astrojs/rss, @astrojs/sitemap, @astrojs/tailwind, &#10;and other packages.&#10;```" />
    <MESSAGE value="feat(backup): add automated backup branch creation" />
    <MESSAGE value="feat: Update config for AstroJS site" />
    <MESSAGE value="feat: remove unnecessary comment in astro.config.mjs (#123)&#10;&#10;This commit removes a now unnecessary comment from &#10;the `astro.config.mjs` file.  The comment was &#10;unnecessary and the configuration is clear without it." />
    <MESSAGE value="feat(ops): Move backup branch creation to setup Node action" />
    <MESSAGE value="feat: refactor Search component structure" />
    <MESSAGE value="feat: Refactor Search component for better readability and maintainability" />
    <MESSAGE value="Fix: Re-attach event listeners after navigation (master)&#10;&#10;Improved the `astro:page-load` event listener to &#10;re-attach event listeners after navigation, &#10;ensuring proper functionality.  Added error &#10;handling and cleared existing search content &#10;before initializing PagefindUI.  Added a placeholder&#10;for `openModal` function." />
    <MESSAGE value="image updated" />
    <MESSAGE value="Updates sitemap URL to dotnetevangelist.com." />
    <MESSAGE value="feat: Add Google AdSense script to BaseHead (master)&#10;&#10;Adds Google AdSense script to the `&lt;head&gt;` section for &#10;monetization purposes.  The script is added asynchronously &#10;to avoid blocking page rendering." />
    <MESSAGE value="feat: Add Google AdSense to header and index page&#10;&#10;Adds Google AdSense script to the header and index &#10;page for monetization.  The script is included &#10;asynchronously to avoid blocking page rendering." />
    <MESSAGE value="```&#10;feat: Update package-lock.json (master)&#10;&#10;Update various packages to their latest versions.  This &#10;includes updates to @babel packages, @rollup packages, &#10;@types/node, and several other dependencies.  No &#10;functional changes are expected.&#10;```" />
    <MESSAGE value="feat: Add blog post about Repository Pattern in .NET&#10;&#10;This commit adds a new blog post explaining the &#10;Repository Pattern in .NET, including code &#10;examples and best practices.  The post covers &#10;defining interfaces, implementing repositories, &#10;and using dependency injection." />
    <MESSAGE value="feat: Update 'implementing-the-repository-pattern-in-dot-net-a-game-changer-for-clean-code' blog with improved structure and added code snippets." />
    <MESSAGE value="Update footer styling, add Google Ads script, set dynamic title, and adjust post display settings" />
    <option name="LAST_COMMIT_MESSAGE" value="Update footer styling, add Google Ads script, set dynamic title, and adjust post display settings" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>