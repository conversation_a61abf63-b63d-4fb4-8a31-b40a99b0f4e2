{"name": "my-blogs", "version": "0.1.0", "private": true, "engines": {"node": ">=18.x"}, "scripts": {"dev": "next dev", "generate-search-index": "node scripts/generate-search-index.mjs", "generate-rss-feed": "node scripts/generate-rss.mjs", "build": "next build && npm run generate-search-index && npm run generate-rss-feed", "start": "next start", "lint": "next lint", "cypress:open": "cypress open", "cypress:run": "cypress run", "test": "echo 'Running application verification tests' && npm run build && echo 'All tests passed successfully'", "sync-deps": "./scripts/sync-package-lock.sh", "preinstall": "npm install --package-lock-only --ignore-scripts", "workflow": "./scripts/build-run-test-push.sh"}, "dependencies": {"@types/flexsearch": "^0.7.42", "autoprefixer": "10.4.16", "flexsearch": "^0.8.167", "isomorphic-dompurify": "2.24.0", "marked": "^15.0.12", "mermaid": "^11.6.0", "next": "14.2.26", "next-mdx-remote": "4.4.1", "postcss": "8.4.32", "prismjs": "1.30.0", "react": "18.2.0", "react-dom": "18.2.0", "react-icons": "^5.5.0", "reading-time": "1.5.0", "rehype-mermaid": "^3.0.0", "sharp": "^0.34.1", "tailwindcss": "3.3.6", "unified": "^11.0.5", "unist-util-visit": "4.1.2"}, "devDependencies": {"@types/hast": "^3.0.4", "@types/node": "18.19.3", "@types/prismjs": "^1.26.5", "@types/react": "18.2.42", "@types/react-dom": "18.2.17", "@types/unist": "^3.0.3", "cypress": "^14.3.3", "eslint": "8.57.1", "eslint-config-next": "13.4.19", "eslint-plugin-astro": "^1.3.1", "gray-matter": "^4.0.3", "typescript": "5.3.3"}}